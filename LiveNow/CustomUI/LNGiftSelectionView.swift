//
//  LNGiftSelectionView.swift
//  LiveNow
//
//  Created by AI on 2025/8/23.
//

import UIKit
import SnapKit

/// 送礼物选择弹框视图
class LNGiftSelectionView: UIView {
    
    // MARK: - Callbacks
    
    /// 发送礼物回调
    var onSendGift: ((LNGiftModel, Int) -> Void)?
    /// 关闭弹框回调
    var onDismiss: (() -> Void)?
    
    // MARK: - UI Components
    
    /// 背景遮罩
    private lazy var backgroundMaskView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tapGesture)
        return view
    }()
    
    /// 主容器视图
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = s(16)
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        return view
    }()
    
    /// 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Select Gift"
        label.font = LNFont.medium(18)
        label.textColor = .black
        label.textAlignment = .center
        return label
    }()
    
    /// 礼物集合视图
    private lazy var giftCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = s(10)
        layout.minimumInteritemSpacing = s(10)
        layout.sectionInset = UIEdgeInsets(top: s(10), left: s(10), bottom: s(10), right: s(10))

        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.isPagingEnabled = true
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(LNGiftCollectionViewCell.self, forCellWithReuseIdentifier: LNGiftCollectionViewCell.identifier)
        return collectionView
    }()
    
    /// 页面指示器
    private lazy var pageControl: UIPageControl = {
        let pageControl = UIPageControl()
        pageControl.currentPageIndicatorTintColor = UIColor.hex(hexString: "#00D4AA")
        pageControl.pageIndicatorTintColor = UIColor.hex(hexString: "#E0E0E0")
        pageControl.hidesForSinglePage = true
        return pageControl
    }()
    
    /// 底部控制区域
    private lazy var bottomControlView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    /// 钻石图标
    private lazy var diamondImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_diamond")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    /// 数量标签
    private lazy var quantityLabel: UILabel = {
        let label = UILabel()
        label.text = "999"
        label.font = LNFont.medium(16)
        label.textColor = UIColor.hex(hexString: "#00D4AA")
        return label
    }()
    
    /// 发送按钮
    private lazy var sendButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Send", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.backgroundColor = UIColor.hex(hexString: "#00D4AA")
        button.layer.cornerRadius = s(20)
        button.addTarget(self, action: #selector(sendButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Properties
    
    /// 礼物数据页面
    private var giftPages: [[LNGiftModel]] = []
    /// 当前选中的礼物
    private var selectedGift: LNGiftModel?
    /// 当前选中的IndexPath
    private var selectedIndexPath: IndexPath?
    /// 礼物数量
    private var giftQuantity: Int = 1
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        loadGiftData()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        loadGiftData()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        // 添加子视图
        addSubview(backgroundMaskView)
        addSubview(containerView)
        
        containerView.addSubview(titleLabel)
        containerView.addSubview(giftCollectionView)
        containerView.addSubview(pageControl)
        containerView.addSubview(bottomControlView)
        
        bottomControlView.addSubview(diamondImageView)
        bottomControlView.addSubview(quantityLabel)
        bottomControlView.addSubview(sendButton)
        
        // 设置约束
        backgroundMaskView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        containerView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(s(320))
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.right.equalToSuperview().inset(s(20))
            make.height.equalTo(s(24))
        }
        
        giftCollectionView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview()
            make.height.equalTo(s(160))
        }
        
        pageControl.snp.makeConstraints { make in
            make.top.equalTo(giftCollectionView.snp.bottom).offset(s(10))
            make.centerX.equalToSuperview()
            make.height.equalTo(s(20))
        }
        
        bottomControlView.snp.makeConstraints { make in
            make.top.equalTo(pageControl.snp.bottom).offset(s(10))
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(s(60))
        }
        
        diamondImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(20))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(20))
        }
        
        quantityLabel.snp.makeConstraints { make in
            make.left.equalTo(diamondImageView.snp.right).offset(s(8))
            make.centerY.equalToSuperview()
        }
        
        sendButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(20))
            make.centerY.equalToSuperview()
            make.width.equalTo(s(80))
            make.height.equalTo(s(40))
        }
    }
    
    // MARK: - Data Loading
    
    private func loadGiftData() {
        giftPages = LNGiftManager.shared.getGiftsByPages()
        pageControl.numberOfPages = giftPages.count
        pageControl.currentPage = 0
        giftCollectionView.reloadData()
    }
    
    // MARK: - Actions
    
    @objc private func backgroundTapped() {
        dismiss()
    }
    
    @objc private func sendButtonTapped() {
        guard let selectedGift = selectedGift else {
            // 显示提示：请选择礼物
            return
        }
        
        onSendGift?(selectedGift, giftQuantity)
        dismiss()
    }
    
    // MARK: - Public Methods
    
    /// 显示弹框
    func show(in parentView: UIView) {
        parentView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 初始状态
        containerView.transform = CGAffineTransform(translationX: 0, y: s(320))
        backgroundMaskView.alpha = 0
        
        // 显示动画
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.containerView.transform = .identity
            self.backgroundMaskView.alpha = 1
        }
    }
    
    /// 隐藏弹框
    func dismiss() {
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseIn) {
            self.containerView.transform = CGAffineTransform(translationX: 0, y: s(320))
            self.backgroundMaskView.alpha = 0
        } completion: { _ in
            self.removeFromSuperview()
            self.onDismiss?()
        }
    }
}

// MARK: - UICollectionViewDataSource

extension LNGiftSelectionView: UICollectionViewDataSource {

    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return giftPages.count
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return giftPages[section].count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: LNGiftCollectionViewCell.identifier, for: indexPath) as! LNGiftCollectionViewCell

        let gift = giftPages[indexPath.section][indexPath.item]
        cell.configure(with: gift)

        // 设置选中状态
        cell.isSelected = (indexPath == selectedIndexPath)

        return cell
    }
}

// MARK: - UICollectionViewDelegate

extension LNGiftSelectionView: UICollectionViewDelegate {

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 取消之前的选中状态
        if let previousIndexPath = selectedIndexPath {
            if let previousCell = collectionView.cellForItem(at: previousIndexPath) as? LNGiftCollectionViewCell {
                previousCell.isSelected = false
            }
        }

        // 设置新的选中状态
        selectedIndexPath = indexPath
        selectedGift = giftPages[indexPath.section][indexPath.item]

        if let cell = collectionView.cellForItem(at: indexPath) as? LNGiftCollectionViewCell {
            cell.isSelected = true
        }
    }
}

// MARK: - UICollectionViewDelegateFlowLayout

extension LNGiftSelectionView: UICollectionViewDelegateFlowLayout {

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        // 每页显示2行4列，计算每个item的大小
        let screenWidth = UIScreen.main.bounds.width
        let sectionInset = UIEdgeInsets(top: s(10), left: s(10), bottom: s(10), right: s(10))
        let interItemSpacing: CGFloat = s(10)
        let itemsPerRow: CGFloat = 4

        let availableWidth = screenWidth - sectionInset.left - sectionInset.right - (interItemSpacing * (itemsPerRow - 1))
        let itemWidth = availableWidth / itemsPerRow
        let itemHeight = s(70)

        return CGSize(width: itemWidth, height: itemHeight)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return s(10)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return s(10)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: s(10), left: s(10), bottom: s(10), right: s(10))
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        return .zero
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForFooterInSection section: Int) -> CGSize {
        return .zero
    }
}

// MARK: - UIScrollViewDelegate

extension LNGiftSelectionView: UIScrollViewDelegate {

    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        let pageWidth = scrollView.frame.width
        let currentPage = Int(scrollView.contentOffset.x / pageWidth)
        pageControl.currentPage = currentPage
    }
}
